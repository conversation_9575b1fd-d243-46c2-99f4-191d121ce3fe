/**
 * BoneBindingPanel - 骨骼绑定管理面板
 * 显示模型的骨骼列表，支持选择骨骼并绑定其他节点的模型
 */

import React, { useState, useCallback } from 'react';
import { ModelInfo, BoneInfo, BoneBinding, GameNodeProperties } from '../../../../src/types/NodeTypes';
import { useNodeSystem } from '../contexts/NodeSystemContext';

interface BoneBindingPanelProps {
  /** 当前选中的节点 */
  selectedNode: GameNodeProperties;
  /** 当前选中的模型 */
  selectedModel: ModelInfo;
  /** 骨骼绑定变更回调 */
  onBoneBindingChange: (nodeId: string, modelId: string, bindings: BoneBinding[]) => void;
}

interface BoneBindingFormData {
  boneName: string;
  boundNodeId: string;
  boundModelId: string;
  positionOffset: { x: number; y: number; z: number };
  rotationOffset: { x: number; y: number; z: number };
  scale: { x: number; y: number; z: number };
}

export const BoneBindingPanel: React.FC<BoneBindingPanelProps> = ({
  selectedNode,
  selectedModel,
  onBoneBindingChange
}) => {
  const { nodes, getNodeModels } = useNodeSystem();
  const [selectedBone, setSelectedBone] = useState<BoneInfo | null>(null);
  const [isCreatingBinding, setIsCreatingBinding] = useState(false);
  const [bindingForm, setBindingForm] = useState<BoneBindingFormData>({
    boneName: '',
    boundNodeId: '',
    boundModelId: '',
    positionOffset: { x: 0, y: 0, z: 0 },
    rotationOffset: { x: 0, y: 0, z: 0 },
    scale: { x: 1, y: 1, z: 1 }
  });

  // 获取可绑定的节点列表（排除当前节点）
  const availableNodes = nodes.filter(node => 
    node.id !== selectedNode.id && node.type === 'mesh'
  );

  // 获取指定节点的模型列表
  const getAvailableModels = useCallback((nodeId: string) => {
    return getNodeModels(nodeId) || [];
  }, [getNodeModels]);

  // 处理骨骼选择
  const handleBoneSelect = useCallback((bone: BoneInfo) => {
    setSelectedBone(bone);
    setBindingForm(prev => ({
      ...prev,
      boneName: bone.name
    }));
  }, []);

  // 开始创建绑定
  const handleStartBinding = useCallback(() => {
    if (!selectedBone) return;
    setIsCreatingBinding(true);
    setBindingForm(prev => ({
      ...prev,
      boneName: selectedBone.name,
      boundNodeId: '',
      boundModelId: '',
      positionOffset: { x: 0, y: 0, z: 0 },
      rotationOffset: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
  }, [selectedBone]);

  // 取消创建绑定
  const handleCancelBinding = useCallback(() => {
    setIsCreatingBinding(false);
    setBindingForm({
      boneName: '',
      boundNodeId: '',
      boundModelId: '',
      positionOffset: { x: 0, y: 0, z: 0 },
      rotationOffset: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    });
  }, []);

  // 确认创建绑定
  const handleConfirmBinding = useCallback(() => {
    if (!selectedBone || !bindingForm.boundNodeId || !bindingForm.boundModelId) {
      return;
    }

    const newBinding: BoneBinding = {
      id: `binding_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      boneName: bindingForm.boneName,
      boundNodeId: bindingForm.boundNodeId,
      boundModelId: bindingForm.boundModelId,
      positionOffset: bindingForm.positionOffset,
      rotationOffset: bindingForm.rotationOffset,
      scale: bindingForm.scale,
      enabled: true,
      createdAt: new Date()
    };

    const currentBindings = selectedModel.boneBindings || [];
    const updatedBindings = [...currentBindings, newBinding];

    onBoneBindingChange(selectedNode.id, selectedModel.id, updatedBindings);
    setIsCreatingBinding(false);
    handleCancelBinding();
  }, [selectedBone, bindingForm, selectedModel, selectedNode.id, onBoneBindingChange]);

  // 删除绑定
  const handleDeleteBinding = useCallback((bindingId: string) => {
    const currentBindings = selectedModel.boneBindings || [];
    const updatedBindings = currentBindings.filter(binding => binding.id !== bindingId);
    onBoneBindingChange(selectedNode.id, selectedModel.id, updatedBindings);
  }, [selectedModel, selectedNode.id, onBoneBindingChange]);

  // 如果模型没有骨骼，显示提示
  if (!selectedModel.hasSkeleton || !selectedModel.bones || selectedModel.bones.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600 text-center">
          此模型不包含骨骼结构，无法进行骨骼绑定
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 骨骼列表 */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          骨骼列表 ({selectedModel.bones.length} 个骨骼)
        </h4>
        <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-md">
          {selectedModel.bones.map((bone, index) => (
            <div
              key={bone.name}
              className={`p-2 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                selectedBone?.name === bone.name ? 'bg-blue-50 border-blue-200' : ''
              }`}
              onClick={() => handleBoneSelect(bone)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">{bone.name}</p>
                  <p className="text-xs text-gray-500">
                    索引: {bone.index}
                    {bone.parentName && ` | 父骨骼: ${bone.parentName}`}
                    {bone.children.length > 0 && ` | 子骨骼: ${bone.children.length}个`}
                  </p>
                </div>
                {selectedBone?.name === bone.name && (
                  <div className="text-blue-600">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 选中骨骼的操作 */}
      {selectedBone && !isCreatingBinding && (
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h5 className="text-sm font-medium text-blue-900">
              已选择骨骼: {selectedBone.name}
            </h5>
            <button
              onClick={handleStartBinding}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              创建绑定
            </button>
          </div>
          
          {/* 显示现有绑定 */}
          {selectedModel.boneBindings && selectedModel.boneBindings.length > 0 && (
            <div className="mt-2">
              <p className="text-xs text-blue-700 mb-1">现有绑定:</p>
              {selectedModel.boneBindings
                .filter(binding => binding.boneName === selectedBone.name)
                .map(binding => {
                  const boundNode = nodes.find(n => n.id === binding.boundNodeId);
                  const boundModels = getAvailableModels(binding.boundNodeId);
                  const boundModel = boundModels.find(m => m.id === binding.boundModelId);
                  
                  return (
                    <div key={binding.id} className="flex items-center justify-between text-xs bg-white p-2 rounded border">
                      <span>
                        {boundNode?.name} - {boundModel?.name}
                      </span>
                      <button
                        onClick={() => handleDeleteBinding(binding.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        删除
                      </button>
                    </div>
                  );
                })}
            </div>
          )}
        </div>
      )}

      {/* 创建绑定表单 */}
      {isCreatingBinding && selectedBone && (
        <div className="p-4 bg-gray-50 rounded-lg space-y-3">
          <div className="flex items-center justify-between">
            <h5 className="text-sm font-medium text-gray-900">
              为骨骼 "{selectedBone.name}" 创建绑定
            </h5>
            <button
              onClick={handleCancelBinding}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 选择目标节点 */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              目标节点
            </label>
            <select
              value={bindingForm.boundNodeId}
              onChange={(e) => {
                setBindingForm(prev => ({
                  ...prev,
                  boundNodeId: e.target.value,
                  boundModelId: '' // 重置模型选择
                }));
              }}
              className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">选择节点...</option>
              {availableNodes.map(node => (
                <option key={node.id} value={node.id}>
                  {node.name} ({node.id})
                </option>
              ))}
            </select>
          </div>

          {/* 选择目标模型 */}
          {bindingForm.boundNodeId && (
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                目标模型
              </label>
              <select
                value={bindingForm.boundModelId}
                onChange={(e) => {
                  setBindingForm(prev => ({
                    ...prev,
                    boundModelId: e.target.value
                  }));
                }}
                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">选择模型...</option>
                {getAvailableModels(bindingForm.boundNodeId).map(model => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* 变换参数 */}
          <div className="grid grid-cols-3 gap-2">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                位置偏移
              </label>
              <div className="space-y-1">
                {(['x', 'y', 'z'] as const).map(axis => (
                  <input
                    key={axis}
                    type="number"
                    step="0.1"
                    placeholder={axis.toUpperCase()}
                    value={bindingForm.positionOffset[axis]}
                    onChange={(e) => {
                      setBindingForm(prev => ({
                        ...prev,
                        positionOffset: {
                          ...prev.positionOffset,
                          [axis]: parseFloat(e.target.value) || 0
                        }
                      }));
                    }}
                    className="w-full px-1 py-0.5 text-xs border border-gray-300 rounded"
                  />
                ))}
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                旋转偏移
              </label>
              <div className="space-y-1">
                {(['x', 'y', 'z'] as const).map(axis => (
                  <input
                    key={axis}
                    type="number"
                    step="0.1"
                    placeholder={axis.toUpperCase()}
                    value={bindingForm.rotationOffset[axis]}
                    onChange={(e) => {
                      setBindingForm(prev => ({
                        ...prev,
                        rotationOffset: {
                          ...prev.rotationOffset,
                          [axis]: parseFloat(e.target.value) || 0
                        }
                      }));
                    }}
                    className="w-full px-1 py-0.5 text-xs border border-gray-300 rounded"
                  />
                ))}
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                缩放
              </label>
              <div className="space-y-1">
                {(['x', 'y', 'z'] as const).map(axis => (
                  <input
                    key={axis}
                    type="number"
                    step="0.1"
                    placeholder={axis.toUpperCase()}
                    value={bindingForm.scale[axis]}
                    onChange={(e) => {
                      setBindingForm(prev => ({
                        ...prev,
                        scale: {
                          ...prev.scale,
                          [axis]: parseFloat(e.target.value) || 1
                        }
                      }));
                    }}
                    className="w-full px-1 py-0.5 text-xs border border-gray-300 rounded"
                  />
                ))}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-2 pt-2">
            <button
              onClick={handleCancelBinding}
              className="px-3 py-1 text-xs text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleConfirmBinding}
              disabled={!bindingForm.boundNodeId || !bindingForm.boundModelId}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              确认绑定
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default BoneBindingPanel;
