/**
 * BoneBindingTest - 骨骼绑定系统测试组件
 * 用于测试和验证骨骼绑定功能
 */

'use client';

import React, { useCallback, useState } from 'react';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { BoneBinding } from '../../../../src/types/NodeTypes';

interface BoneBindingTestProps {
  className?: string;
}

export const BoneBindingTest: React.FC<BoneBindingTestProps> = ({ className = '' }) => {
  const { nodes, getNodeModels } = useNodeSystem();
  const [testResults, setTestResults] = useState<string[]>([]);

  // 添加测试结果
  const addTestResult = useCallback((message: string) => {
    setTestResults(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  }, []);

  // 清除测试结果
  const clearTestResults = useCallback(() => {
    setTestResults([]);
  }, []);

  // 测试骨骼绑定管理器
  const testBoneBindingManager = useCallback(() => {
    addTestResult('开始测试骨骼绑定管理器...');

    const boneBindingManager = (window as unknown as { 
      boneBindingManager?: { 
        getBindingStats: () => { totalNodes: number; totalBindings: number };
        getNodeBindingCount: (nodeId: string) => number;
      } 
    }).boneBindingManager;

    if (!boneBindingManager) {
      addTestResult('❌ 骨骼绑定管理器未找到');
      return;
    }

    addTestResult('✅ 骨骼绑定管理器已找到');

    // 获取绑定统计
    const stats = boneBindingManager.getBindingStats();
    addTestResult(`📊 绑定统计: ${stats.totalNodes} 个节点, ${stats.totalBindings} 个绑定`);

    // 测试每个节点的绑定数量
    nodes.forEach(node => {
      const bindingCount = boneBindingManager.getNodeBindingCount(node.id);
      if (bindingCount > 0) {
        addTestResult(`🔗 节点 ${node.name} (${node.id}) 有 ${bindingCount} 个活跃绑定`);
      }
    });

    addTestResult('✅ 骨骼绑定管理器测试完成');
  }, [nodes, addTestResult]);

  // 测试节点模型的骨骼信息
  const testNodeBoneInfo = useCallback(() => {
    addTestResult('开始测试节点骨骼信息...');

    let totalModelsWithBones = 0;
    let totalBoneBindings = 0;

    nodes.forEach(node => {
      const models = getNodeModels(node.id);
      if (!models || models.length === 0) {
        return;
      }

      models.forEach(model => {
        if (model.hasSkeleton && model.bones && model.bones.length > 0) {
          totalModelsWithBones++;
          addTestResult(`🦴 模型 ${model.name} 有 ${model.bones.length} 个骨骼`);
          
          // 显示前3个骨骼的信息
          model.bones.slice(0, 3).forEach(bone => {
            addTestResult(`  - 骨骼: ${bone.name} (索引: ${bone.index})`);
          });

          if (model.bones.length > 3) {
            addTestResult(`  - ... 还有 ${model.bones.length - 3} 个骨骼`);
          }
        }

        if (model.boneBindings && model.boneBindings.length > 0) {
          totalBoneBindings += model.boneBindings.length;
          addTestResult(`🔗 模型 ${model.name} 有 ${model.boneBindings.length} 个骨骼绑定`);
          
          model.boneBindings.forEach(binding => {
            addTestResult(`  - 绑定: ${binding.boneName} -> 节点 ${binding.boundNodeId}`);
          });
        }
      });
    });

    addTestResult(`📊 总计: ${totalModelsWithBones} 个带骨骼的模型, ${totalBoneBindings} 个骨骼绑定`);
    addTestResult('✅ 节点骨骼信息测试完成');
  }, [nodes, getNodeModels, addTestResult]);

  // 测试Three.js场景中的骨骼对象
  const testThreeJsBones = useCallback(() => {
    addTestResult('开始测试Three.js场景中的骨骼对象...');

    const threeScene = (window as unknown as { threeScene?: any }).threeScene;
    if (!threeScene) {
      addTestResult('❌ Three.js场景未找到');
      return;
    }

    addTestResult('✅ Three.js场景已找到');

    let totalBones = 0;
    let totalSkinnedMeshes = 0;

    threeScene.traverse((object: any) => {
      if (object.type === 'Bone') {
        totalBones++;
        if (totalBones <= 5) { // 只显示前5个骨骼
          addTestResult(`🦴 发现骨骼: ${object.name || '未命名'} (${object.uuid})`);
        }
      }
      
      if (object.type === 'SkinnedMesh') {
        totalSkinnedMeshes++;
        if (totalSkinnedMeshes <= 3) { // 只显示前3个蒙皮网格
          addTestResult(`🎭 发现蒙皮网格: ${object.name || '未命名'}`);
          if (object.skeleton && object.skeleton.bones) {
            addTestResult(`  - 骨骼数量: ${object.skeleton.bones.length}`);
          }
        }
      }
    });

    if (totalBones > 5) {
      addTestResult(`  - ... 还有 ${totalBones - 5} 个骨骼`);
    }

    if (totalSkinnedMeshes > 3) {
      addTestResult(`  - ... 还有 ${totalSkinnedMeshes - 3} 个蒙皮网格`);
    }

    addTestResult(`📊 场景统计: ${totalBones} 个骨骼, ${totalSkinnedMeshes} 个蒙皮网格`);
    addTestResult('✅ Three.js骨骼对象测试完成');
  }, [addTestResult]);

  // 运行所有测试
  const runAllTests = useCallback(() => {
    clearTestResults();
    addTestResult('🚀 开始运行所有骨骼绑定测试...');
    
    setTimeout(() => testBoneBindingManager(), 100);
    setTimeout(() => testNodeBoneInfo(), 200);
    setTimeout(() => testThreeJsBones(), 300);
    setTimeout(() => addTestResult('🎉 所有测试完成!'), 400);
  }, [testBoneBindingManager, testNodeBoneInfo, testThreeJsBones, addTestResult, clearTestResults]);

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">骨骼绑定系统测试</h3>
        <div className="flex gap-2">
          <button
            onClick={runAllTests}
            className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
          >
            运行所有测试
          </button>
          <button
            onClick={clearTestResults}
            className="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition-colors"
          >
            清除结果
          </button>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex gap-2">
          <button
            onClick={testBoneBindingManager}
            className="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors"
          >
            测试绑定管理器
          </button>
          <button
            onClick={testNodeBoneInfo}
            className="px-3 py-1 bg-purple-500 text-white text-sm rounded hover:bg-purple-600 transition-colors"
          >
            测试骨骼信息
          </button>
          <button
            onClick={testThreeJsBones}
            className="px-3 py-1 bg-orange-500 text-white text-sm rounded hover:bg-orange-600 transition-colors"
          >
            测试场景骨骼
          </button>
        </div>

        {/* 测试结果显示区域 */}
        <div className="bg-gray-50 rounded p-3 max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500 text-sm">点击测试按钮开始测试...</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono text-gray-700">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BoneBindingTest;
