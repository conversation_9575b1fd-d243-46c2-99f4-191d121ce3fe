/**
 * ThreeSceneViewer - Three.js场景查看器组件
 * 替代原来的Babylon.js场景，提供3D场景渲染和交互功能
 */

'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import dynamic from 'next/dynamic';

// 动态导入ThreeCanvas以避免SSR问题
const ThreeCanvas = dynamic(
  () => import('../../../../src/three/components/ThreeCanvas').then(mod => ({ default: mod.ThreeCanvas })),
  {
    ssr: false,
    loading: () => <div className="flex items-center justify-center h-full bg-gray-100">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p className="text-sm text-gray-600">加载Three.js场景...</p>
      </div>
    </div>
  }
);

import type { ThreeCanvasContext } from '../../../../src/three/components/ThreeCanvas';
import { ThreeUtils } from '../../../../src/three/core/ThreeUtils';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { ModelInfo, MaterialInfo, AnimationInfo } from '../../../../src/types/NodeTypes';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { ThreeNodeFactory } from '../../../../src/utils/ThreeNodeFactory';
import { NodeRegistry } from '../../../../src/utils/NodeRegistry';
import { BoneBindingManager } from '../../../../src/three/managers/BoneBindingManager';

interface ThreeSceneViewerProps {
  className?: string;
}

export const ThreeSceneViewer: React.FC<ThreeSceneViewerProps> = ({ className = '' }) => {
  const {
    nodes,
    selectedNode,
    setNodes,
    setNodeStats,
    setSelectedNode,
    setThreeContext,
    threeContext,
    addModel,
    addMaterial,
    addAnimation
  } = useNodeSystem();

  const controlsRef = useRef<OrbitControls | null>(null);
  const nodeFactoryRef = useRef<ThreeNodeFactory | null>(null);
  const nodeRegistryRef = useRef<NodeRegistry | null>(null);
  const boneBindingManagerRef = useRef<BoneBindingManager | null>(null);

  // 已移除applyModelToScene和applyMaterialToScene函数，逻辑已内联到handleThreeInitialized中

  // 已移除loadSavedNodeProperties和createBasicThreeScene函数，逻辑已内联到handleThreeInitialized中

  // 初始化Three.js场景
  const handleThreeInitialized = useCallback((context: ThreeCanvasContext) => {
    console.log('[ThreeSceneViewer] Three.js场景初始化');

    setThreeContext(context);

    // 暴露Three.js对象到全局，供脚本执行使用
    (window as any).threeScene = context.scene;
    (window as any).threeCamera = context.camera;
    (window as any).threeRenderer = context.renderer;
    (window as any).THREE = THREE;
    console.log('[ThreeSceneViewer] Three.js对象已暴露到全局');
    
    // 创建轨道控制器
    const controls = new OrbitControls(context.camera, context.renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.screenSpacePanning = false;
    controls.minDistance = 1;
    controls.maxDistance = 100;
    controls.maxPolarAngle = Math.PI / 2;
    
    // 默认禁用控制器（视角锁定）
    controls.enabled = false;
    
    controlsRef.current = controls;

    // 暴露OrbitControls到全局，供属性更新时使用
    (window as any).orbitControls = controls;

    // 暴露骨骼绑定管理器到全局
    (window as any).boneBindingManager = boneBindingManagerRef.current;

    // 初始化节点系统
    nodeFactoryRef.current = ThreeNodeFactory.getInstance();
    nodeRegistryRef.current = NodeRegistry.getInstance();
    boneBindingManagerRef.current = new BoneBindingManager(context.scene);

    // 清理现有节点
    nodeFactoryRef.current.clearAllNodes();

    // 创建基础3D场景（内联实现）
    if (nodeFactoryRef.current) {
      const { scene, camera } = context;

      // 使用ThreeNodeFactory创建基础3D场景，传入现有摄像机
      nodeFactoryRef.current.createBasic3DScene(scene, camera);

      // 添加网格辅助线
      const gridHelper = ThreeUtils.createGridHelper(20, 20);
      scene.add(gridHelper);

      console.log('[ThreeSceneViewer] 基础场景创建完成');
    }

    // 注册节点变化监听器
    nodeRegistryRef.current.addObserver((updatedNodes) => {
      setNodes([...updatedNodes]);
      setNodeStats(nodeFactoryRef.current?.getNodeStats() || { total: 0, byType: {} });
    });

    // 更新节点状态
    setNodes(nodeRegistryRef.current.getAllNodes());
    setNodeStats(nodeFactoryRef.current.getNodeStats());

    // 场景创建完成后，加载已保存的节点属性（内联实现）
    setTimeout(async () => {
      console.log('[ThreeSceneViewer] 开始加载已保存的节点属性...');

      try {
        const response = await fetch('/api/node-properties');
        const result = await response.json();

        if (result.success && result.data.nodes.length > 0) {
          console.log('[ThreeSceneViewer] 加载已保存的节点属性:', result.data.nodes);

          const { scene } = context;
          console.log('[ThreeSceneViewer] 当前场景中的对象:', scene.children.map(child => ({ name: child.name, type: child.type })));

          // 应用已保存的属性到Three.js对象
          result.data.nodes.forEach((savedNode: any) => {
            let object = scene.getObjectByName(savedNode.id);

            // 如果对象不存在，先创建它
            if (!object) {
              console.log(`[ThreeSceneViewer] 创建新节点: ${savedNode.id}`);

              // 使用ThreeNodeFactory创建新的网格节点
              if (nodeFactoryRef.current) {
                const newNode = nodeFactoryRef.current.createBoxMesh(scene, {
                  id: savedNode.id,
                  name: savedNode.name || '未命名节点',
                  size: 1,
                  position: new THREE.Vector3(
                    savedNode.position?.x || 0,
                    savedNode.position?.y || 0,
                    savedNode.position?.z || 0
                  ),
                  color: savedNode.color ? new THREE.Color(
                    savedNode.color.r,
                    savedNode.color.g,
                    savedNode.color.b
                  ) : new THREE.Color(0x00ff00)
                });

                object = scene.getObjectByName(savedNode.id);
                console.log(`[ThreeSceneViewer] 新节点创建完成: ${savedNode.id}`);
              }
            }

            if (object) {
              // 应用位置、旋转、缩放到Three.js对象
              if (savedNode.position) {
                object.position.set(
                  savedNode.position.x || 0,
                  savedNode.position.y || 0,
                  savedNode.position.z || 0
                );
              }
              if (savedNode.rotation) {
                object.rotation.set(
                  savedNode.rotation.x || 0,
                  savedNode.rotation.y || 0,
                  savedNode.rotation.z || 0
                );
              }
              if (savedNode.scaling) {
                object.scale.set(
                  savedNode.scaling.x || 1,
                  savedNode.scaling.y || 1,
                  savedNode.scaling.z || 1
                );
              }
              
              // 如果是摄像机，应用摄像机特有属性
              if (object instanceof THREE.Camera) {
                const camera = object as THREE.PerspectiveCamera;
                
                // 应用FOV
                if (savedNode.fov !== undefined) {
                  camera.fov = Number(savedNode.fov);
                  camera.updateProjectionMatrix();
                  console.log(`[ThreeSceneViewer] 摄像机FOV已应用: ${camera.fov}`);
                }
                
                // 应用视角锁定状态
                if (savedNode.viewLocked !== undefined) {
                  const controls = (window as any).orbitControls;
                  if (controls) {
                    controls.enabled = !savedNode.viewLocked;
                    console.log(`[ThreeSceneViewer] 摄像机视角锁定状态已应用: ${savedNode.viewLocked ? '已锁定' : '已解锁'}`);
                  }
                }
              }

              // 同时更新NodeRegistry中的节点属性，确保UI显示正确的值
              if (nodeRegistryRef.current) {
                const existingNode = nodeRegistryRef.current.getNode(savedNode.id);
                if (existingNode) {
                  const updatedNode = {
                    ...existingNode,
                    name: savedNode.name !== undefined ? savedNode.name : existingNode.name,
                    position: savedNode.position ? new THREE.Vector3(
                      savedNode.position.x || 0,
                      savedNode.position.y || 0,
                      savedNode.position.z || 0
                    ) : existingNode.position,
                    rotation: savedNode.rotation ? new THREE.Vector3(
                      savedNode.rotation.x || 0,
                      savedNode.rotation.y || 0,
                      savedNode.rotation.z || 0
                    ) : existingNode.rotation,
                    scaling: savedNode.scaling ? new THREE.Vector3(
                      savedNode.scaling.x || 1,
                      savedNode.scaling.y || 1,
                      savedNode.scaling.z || 1
                    ) : existingNode.scaling,
                    // 如果是摄像机，更新摄像机特有属性
                    ...(existingNode.type === 'camera' && {
                      fov: savedNode.fov !== undefined ? Number(savedNode.fov) : (existingNode as any).fov,
                      viewLocked: savedNode.viewLocked !== undefined ? Boolean(savedNode.viewLocked) : (existingNode as any).viewLocked
                    })
                  };

                  // 如果有颜色信息，也更新到节点属性中
                  if (savedNode.color && 'material' in existingNode) {
                    const meshNode = updatedNode as any;
                    if (meshNode.material) {
                      meshNode.material = {
                        ...meshNode.material,
                        diffuseColor: new THREE.Color(savedNode.color.r, savedNode.color.g, savedNode.color.b)
                      };
                    }
                  }

                  nodeRegistryRef.current.updateNode(savedNode.id, updatedNode);
                  console.log(`[ThreeSceneViewer] 已同步更新NodeRegistry中的节点: ${savedNode.id}`);
                }
              }

              // 应用颜色
              if (savedNode.color) {
                object.traverse((child) => {
                  if (child instanceof THREE.Mesh) {
                    const material = child.material as THREE.MeshStandardMaterial;
                    if (material && material.color) {
                      material.color.setRGB(savedNode.color.r, savedNode.color.g, savedNode.color.b);
                    }
                  }
                });
              }

              // 恢复模型数据到Context
              if (savedNode.models && savedNode.models.length > 0) {
                savedNode.models.forEach((modelData: any) => {
                  const modelInfo: ModelInfo = {
                    id: modelData.id,
                    name: modelData.name,
                    filePath: modelData.filePath,
                    fileSize: modelData.fileSize,
                    fileType: modelData.fileType as 'fbx' | 'glb' | 'gltf',
                    isDefault: modelData.isDefault,
                    uploadedAt: new Date(modelData.uploadedAt),
                    meshCount: modelData.meshCount || 0,
                    materialCount: modelData.materialCount || 0,
                    hasAnimations: modelData.hasAnimations || false,
                    bones: modelData.bones || [],
                    boneBindings: modelData.boneBindings || [],
                    hasSkeleton: modelData.hasSkeleton || false
                  };
                  addModel(savedNode.id, modelInfo);

                  // 异步应用默认模型到场景中
                  if (modelData.isDefault || savedNode.models.length === 1) {
                    setTimeout(async () => {
                      try {
                        // 内联模型应用逻辑
                        const existingObject = scene.getObjectByName(savedNode.id);
                        if (!existingObject) {
                          console.error(`未找到节点对象: ${savedNode.id}`);
                          return;
                        }

                        let loadedModel: THREE.Group;
                        if (modelData.fileType === 'fbx') {
                          const fbxLoader = new FBXLoader();
                          loadedModel = await new Promise<THREE.Group>((resolve, reject) => {
                            fbxLoader.load(modelData.filePath, resolve, undefined, reject);
                          });
                        } else if (modelData.fileType === 'glb' || modelData.fileType === 'gltf') {
                          const gltfLoader = new GLTFLoader();
                          const gltf = await new Promise<any>((resolve, reject) => {
                            gltfLoader.load(modelData.filePath, resolve, undefined, reject);
                          });
                          loadedModel = gltf.scene;
                        } else {
                          throw new Error(`不支持的模型格式: ${modelData.fileType}`);
                        }

                        // 清除现有的子对象
                        const childrenToRemove = existingObject.children.filter(child =>
                          child.userData.isLoadedModel
                        );
                        childrenToRemove.forEach(child => existingObject.remove(child));

                        // 设置模型属性
                        loadedModel.userData.isLoadedModel = true;
                        loadedModel.traverse((child) => {
                          if (child instanceof THREE.Mesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                          }
                        });

                        // 添加到现有节点
                        existingObject.add(loadedModel);
                        console.log(`[ThreeSceneViewer] 已应用模型到场景: ${modelData.name}`);

                        // 应用骨骼绑定（如果存在）
                        if (boneBindingManagerRef.current && savedNode.models) {
                          const allModels = savedNode.models.map((m: any) => ({
                            id: m.id,
                            name: m.name,
                            filePath: m.filePath,
                            fileSize: m.fileSize,
                            fileType: m.fileType,
                            isDefault: m.isDefault,
                            uploadedAt: new Date(m.uploadedAt),
                            meshCount: m.meshCount || 0,
                            materialCount: m.materialCount || 0,
                            hasAnimations: m.hasAnimations || false,
                            bones: m.bones || [],
                            boneBindings: m.boneBindings || [],
                            hasSkeleton: m.hasSkeleton || false
                          }));

                          setTimeout(() => {
                            boneBindingManagerRef.current?.applyNodeBoneBindings(savedNode.id, allModels);
                          }, 200);
                        }
                      } catch (error) {
                        console.error(`[ThreeSceneViewer] 应用模型失败: ${modelData.name}`, error);
                      }
                    }, 100);
                  }
                });
              }

              // 恢复材质数据到Context并应用默认材质
              if (savedNode.materials && savedNode.materials.length > 0) {
                savedNode.materials.forEach((materialData: any) => {
                  const materialInfo: MaterialInfo = {
                    id: materialData.id,
                    name: materialData.name,
                    filePath: materialData.filePath,
                    fileSize: materialData.fileSize,
                    fileType: materialData.fileType,
                    isDefault: materialData.isDefault,
                    uploadedAt: new Date(materialData.uploadedAt),
                    materialType: materialData.materialType
                  };
                  addMaterial(savedNode.id, materialInfo);

                  // 异步应用默认材质到场景中
                  if (materialData.isDefault) {
                    setTimeout(async () => {
                      try {
                        // 内联材质应用逻辑
                        const existingObject = scene.getObjectByName(savedNode.id);
                        if (!existingObject) {
                          console.error(`未找到节点对象: ${savedNode.id}`);
                          return;
                        }

                        // 加载材质纹理
                        const textureLoader = new THREE.TextureLoader();
                        const texture = await new Promise<THREE.Texture>((resolve, reject) => {
                          textureLoader.load(materialData.filePath, resolve, undefined, reject);
                        });

                        // 应用材质到模型
                        existingObject.traverse((child) => {
                          if (child instanceof THREE.Mesh) {
                            let newMaterial: THREE.Material;

                            switch (materialData.materialType) {
                              case 'normal':
                                newMaterial = new THREE.MeshStandardMaterial({
                                  normalMap: texture,
                                  color: 0xffffff
                                });
                                break;
                              case 'diffuse':
                              default:
                                newMaterial = new THREE.MeshStandardMaterial({
                                  map: texture,
                                  color: 0xffffff
                                });
                                break;
                            }

                            // 保存原始材质
                            if (!child.userData.originalMaterial) {
                              child.userData.originalMaterial = child.material;
                            }

                            child.material = newMaterial;
                            child.material.needsUpdate = true;
                          }
                        });

                        console.log(`[ThreeSceneViewer] 已应用材质到场景: ${materialData.name}`);
                      } catch (error) {
                        console.error(`[ThreeSceneViewer] 应用材质失败: ${materialData.name}`, error);
                      }
                    }, 200);
                  }
                });
              }

              // 恢复动画数据到Context并应用默认动画
              if (savedNode.animations && savedNode.animations.length > 0) {
                let defaultAnimation: any = null;

                savedNode.animations.forEach((animationData: any) => {
                  const animationInfo: AnimationInfo = {
                    id: animationData.id,
                    name: animationData.name,
                    filePath: animationData.filePath,
                    duration: animationData.duration,
                    isDefault: animationData.isDefault,
                    uploadedAt: new Date(animationData.uploadedAt),
                    clips: animationData.clips || []
                  };
                  addAnimation(savedNode.id, animationInfo);

                  // 记录默认动画
                  if (animationData.isDefault) {
                    defaultAnimation = animationData;
                  }
                });

                // 异步应用默认动画到场景中
                if (defaultAnimation) {
                  setTimeout(async () => {
                    try {
                      // 内联动画应用逻辑
                      const existingObject = scene.getObjectByName(savedNode.id);
                      if (!existingObject) {
                        console.error(`未找到节点对象: ${savedNode.id}`);
                        return;
                      }

                      // 加载并应用动画
                      const fbxLoader = new FBXLoader();
                      const animationModel = await new Promise<THREE.Group>((resolve, reject) => {
                        fbxLoader.load(defaultAnimation.filePath, resolve, undefined, reject);
                      });

                      if (animationModel.animations && animationModel.animations.length > 0) {
                        // 创建动画混合器
                        const mixer = new THREE.AnimationMixer(existingObject);

                        // 设置合适的时间缩放，降低播放速度
                        mixer.timeScale = 0.5; // 降低到一半速度，可以根据需要调整

                        const action = mixer.clipAction(animationModel.animations[0]);

                        // 设置动画循环
                        action.setLoop(THREE.LoopRepeat, Infinity);
                        action.play();

                        // 保存混合器引用以便后续更新
                        existingObject.userData.animationMixer = mixer;
                        existingObject.userData.currentAction = action;

                        console.log(`[ThreeSceneViewer] 已应用默认动画到场景: ${defaultAnimation.name}`);
                      }
                    } catch (error) {
                      console.error(`[ThreeSceneViewer] 应用默认动画失败: ${defaultAnimation.name}`, error);
                    }
                  }, 300); // 延迟确保模型先加载完成
                }
              }

              console.log(`[ThreeSceneViewer] 已应用属性到节点: ${savedNode.id}`);
            }
          });

          // 更新节点状态以反映加载的属性
          if (nodeRegistryRef.current) {
            setNodes(nodeRegistryRef.current.getAllNodes());
          }
        }
      } catch (error) {
        console.error('[ThreeSceneViewer] 加载节点属性失败:', error);
      }
    }, 100); // 稍微延迟确保所有对象都已添加到场景中

  }, [setThreeContext, setNodes, setNodeStats, addModel, addMaterial, addAnimation]);



  // 渲染循环回调
  const handleRender = useCallback((_context: ThreeCanvasContext, deltaTime: number) => {
    // 更新控制器
    if (controlsRef.current) {
      controlsRef.current.update();
    }

    // 更新所有动画混合器
    // 注意：这里我们需要从全局状态或其他方式获取动画混合器
    // 由于架构限制，我们将在NodePropertiesPanel中直接处理动画更新

    // 遍历场景中的所有对象，查找并更新动画混合器
    if (_context.scene) {
      _context.scene.traverse((object) => {
        if (object.userData.animationMixer) {
          object.userData.animationMixer.update(deltaTime);
        }
      });
    }
  }, []);

  // 添加随机方块
  const addRandomBox = useCallback(() => {
    if (!threeContext || !nodeFactoryRef.current) return;

    const { scene } = threeContext;
    const id = `box_${Date.now()}`;

    // 使用ThreeNodeFactory创建方块
    const box = nodeFactoryRef.current.createBoxMesh(scene, {
      id,
      name: `随机方块_${Math.floor(Math.random() * 1000)}`,
      size: 0.5 + Math.random() * 1.5,
      position: new THREE.Vector3(
        (Math.random() - 0.5) * 10,
        Math.random() * 3 + 0.5,
        (Math.random() - 0.5) * 10
      ),
      color: new THREE.Color(Math.random(), Math.random(), Math.random())
    });

    // 更新节点状态
    if (nodeRegistryRef.current) {
      setNodes(nodeRegistryRef.current.getAllNodes());
      setNodeStats(nodeFactoryRef.current.getNodeStats());
    }

    console.log('[ThreeSceneViewer] 添加随机方块:', box.name);
  }, [threeContext, setNodes, setNodeStats]);

  // 移除选中节点
  const removeSelectedNode = useCallback(() => {
    if (!selectedNode || !threeContext) return;

    const { scene } = threeContext;
    const object = scene.getObjectByName(selectedNode.id);
    
    if (object) {
      scene.remove(object);
      ThreeUtils.disposeObject(object);
    }

    if (nodeRegistryRef.current) {
      nodeRegistryRef.current.unregister(selectedNode.id);
    }

    setSelectedNode(null);
    console.log('[ThreeSceneViewer] 移除节点:', selectedNode.id);
  }, [selectedNode, threeContext, setSelectedNode]);

  // 重置场景
  const resetScene = useCallback(() => {
    if (!threeContext || !nodeFactoryRef.current || !nodeRegistryRef.current) return;

    const { scene } = threeContext;

    // 清理场景中的所有对象（除了光照和相机）
    const objectsToRemove: THREE.Object3D[] = [];
    scene.traverse((child) => {
      if (child !== scene && !(child instanceof THREE.Light) && !(child instanceof THREE.Camera)) {
        objectsToRemove.push(child);
      }
    });

    objectsToRemove.forEach(obj => {
      scene.remove(obj);
      ThreeUtils.disposeObject(obj);
    });

    // 清理节点系统
    nodeFactoryRef.current.clearAllNodes();

    // 清理骨骼绑定
    if (boneBindingManagerRef.current) {
      boneBindingManagerRef.current.clearAllBindings();
    }
    
    // 重新创建基础场景（内联实现）
    if (nodeFactoryRef.current && threeContext) {
      const { scene } = threeContext;

      // 使用ThreeNodeFactory创建基础3D场景
      nodeFactoryRef.current.createBasic3DScene(scene);

      // 添加网格辅助线
      const gridHelper = ThreeUtils.createGridHelper(20, 20);
      scene.add(gridHelper);

      console.log('[ThreeSceneViewer] 基础场景重新创建完成');
    }

    // 更新状态
    setNodes(nodeRegistryRef.current.getAllNodes());
    setNodeStats(nodeFactoryRef.current.getNodeStats());
    setSelectedNode(null);

    console.log('[ThreeSceneViewer] 场景已重置');
  }, [threeContext, setNodes, setNodeStats, setSelectedNode]);



  // 动画更新循环
  useEffect(() => {
    if (!threeContext) return;

    const { scene } = threeContext;
    let animationId: number;
    const clock = new THREE.Clock();

    const updateAnimations = () => {
      // 使用Three.js Clock获取准确的时间增量
      const delta = clock.getDelta();

      scene.traverse((child) => {
        if (child.userData.animationMixer) {
          child.userData.animationMixer.update(delta);
        }
      });

      animationId = requestAnimationFrame(updateAnimations);
    };

    updateAnimations();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [threeContext]);

  // 清理资源
  useEffect(() => {
    return () => {
      if (controlsRef.current) {
        controlsRef.current.dispose();
      }
    };
  }, []);

  return (
    <div className={`flex-1 flex flex-col ${className}`}>
      {/* 3D Canvas区域 */}
      <div className="flex-1 relative bg-gray-100">
        <ThreeCanvas
          className="w-full h-full"
          engineId="node-system-demo"
          debugMode={process.env.NODE_ENV === 'development'}
          backgroundColor={0xa0a0a0}
          enableShadows={true}
          enableFog={true}
          onInitialized={handleThreeInitialized}
          onRender={handleRender}
        />
        
        {/* 场景控制按钮 */}
        <div className="absolute top-4 left-4 space-y-2">
          <button
            onClick={addRandomBox}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors shadow-sm"
          >
            ➕ 添加方块
          </button>
          
          <button
            onClick={removeSelectedNode}
            disabled={!selectedNode}
            className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-3 py-2 rounded text-sm font-medium transition-colors shadow-sm"
          >
            🗑️ 删除选中
          </button>
          
          <button
            onClick={resetScene}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors shadow-sm"
          >
            🔄 重置场景
          </button>
        </div>

        {/* 场景信息显示 */}
        <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded text-sm">
          <div>节点总数: {nodes.length}</div>
          <div>选中: {selectedNode ? selectedNode.name : '无'}</div>
          <div>引擎: Three.js</div>
        </div>
      </div>
    </div>
  );
};

export default ThreeSceneViewer;
