/**
 * HTML导出核心模块
 * 实现HTML导出的主要逻辑和流程控制
 */

import { 
  HTMLExporter, 
  ExportOptions, 
  ExportResult, 
  ExportProgress, 
  GameProject, 
  HTMLExportConfig,
  DEFAULT_EXPORT_CONFIG 
} from './types';
import { ProjectManager } from './ProjectManager';
import { TypeScriptCompiler } from './TypeScriptCompiler';
import { DependencyBundler } from './DependencyBundler';
import { AssetProcessor } from './AssetProcessor';
import { HTMLTemplateEngine } from './HTMLTemplateEngine';
import { CodeOptimizer } from './CodeOptimizer';

/**
 * HTML导出器核心实现
 */
export class HTMLExportCore implements HTMLExporter {
  private projectManager: ProjectManager;
  private compiler: TypeScriptCompiler;
  private bundler: DependencyBundler;
  private assetProcessor: AssetProcessor;
  private templateEngine: HTMLTemplateEngine;
  private optimizer: CodeOptimizer;

  constructor() {
    this.projectManager = new ProjectManager();
    this.compiler = new TypeScriptCompiler();
    this.bundler = new DependencyBundler();
    this.assetProcessor = new AssetProcessor();
    this.templateEngine = new HTMLTemplateEngine();
    this.optimizer = new CodeOptimizer();
  }

  /**
   * 导出项目为HTML文件
   */
  async export(options: ExportOptions): Promise<ExportResult> {
    const startTime = Date.now();
    const warnings: string[] = [];
    
    try {
      // 1. 准备阶段
      this.reportProgress(options, 'preparing', 0, '准备导出...');
      
      const project = await this.loadProject(options.project);
      const config = this.mergeConfig(project.config, options.config);
      
      // 验证项目
      const validation = await this.validate(project);
      if (!validation.valid) {
        throw new Error(`项目验证失败: ${validation.errors.join(', ')}`);
      }

      // 2. 编译阶段
      this.reportProgress(options, 'compiling', 20, '编译TypeScript代码...');
      
      const compilationResult = await this.compiler.compile(project, config);
      if (compilationResult.errors.length > 0) {
        throw new Error(`编译失败: ${compilationResult.errors.join(', ')}`);
      }
      warnings.push(...compilationResult.warnings);

      // 3. 打包依赖
      this.reportProgress(options, 'bundling', 40, '处理依赖...');
      
      const bundleResult = await this.bundler.bundle(project, config);
      warnings.push(...bundleResult.warnings);

      // 4. 处理资源
      this.reportProgress(options, 'bundling', 60, '处理资源文件...');
      
      const assetResult = await this.assetProcessor.process(project, config);
      warnings.push(...assetResult.warnings);

      // 5. 优化代码
      this.reportProgress(options, 'optimizing', 80, '优化代码...');
      
      const optimizedCode = await this.optimizer.optimize({
        javascript: compilationResult.code,
        css: compilationResult.css || '',
        html: '',
        dependencies: bundleResult.bundledCode,
        assets: assetResult.processedAssets
      }, config);

      // 6. 生成HTML
      this.reportProgress(options, 'finalizing', 90, '生成HTML文件...');
      
      const htmlContent = await this.templateEngine.generate({
        title: project.name,
        description: project.description || '',
        gameCode: optimizedCode.javascript,
        styles: optimizedCode.css,
        dependencies: bundleResult.dependencies,
        assets: assetResult.assetMap,
        config: config
      }, config);

      // 7. 保存文件
      const outputPath = await this.saveOutput(htmlContent, config, options.outputDir);
      const outputSize = Buffer.byteLength(htmlContent, 'utf8');

      this.reportProgress(options, 'complete', 100, '导出完成');

      const duration = Date.now() - startTime;

      return {
        success: true,
        outputPath,
        outputSize,
        duration,
        warnings,
        details: {
          compilation: {
            sourceFiles: project.sourceFiles.length,
            compiledFiles: compilationResult.compiledFiles,
            errors: 0,
            warnings: compilationResult.warnings.length
          },
          assets: {
            totalFiles: project.assetFiles.length,
            inlinedFiles: assetResult.inlinedCount,
            totalSize: assetResult.totalSize,
            compressedSize: assetResult.compressedSize
          },
          dependencies: {
            totalDependencies: project.dependencies.length,
            bundledDependencies: bundleResult.bundledCount,
            cdnDependencies: bundleResult.cdnCount
          }
        }
      };

    } catch (error) {
      this.reportProgress(options, 'error', 0, `导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
      
      return {
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : '未知错误',
        warnings,
        details: {
          compilation: { sourceFiles: 0, compiledFiles: 0, errors: 1, warnings: 0 },
          assets: { totalFiles: 0, inlinedFiles: 0, totalSize: 0, compressedSize: 0 },
          dependencies: { totalDependencies: 0, bundledDependencies: 0, cdnDependencies: 0 }
        }
      };
    }
  }

  /**
   * 验证项目
   */
  async validate(project: GameProject): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // 检查项目基本信息
    if (!project.name || project.name.trim() === '') {
      errors.push('项目名称不能为空');
    }

    // 检查源文件
    if (!project.sourceFiles || project.sourceFiles.length === 0) {
      errors.push('项目必须包含至少一个源文件');
    }

    // 检查入口文件
    const entryFiles = project.sourceFiles.filter(f => f.isEntry);
    if (entryFiles.length === 0) {
      errors.push('项目必须指定一个入口文件');
    } else if (entryFiles.length > 1) {
      errors.push('项目只能有一个入口文件');
    }

    // 检查TypeScript文件语法
    for (const file of project.sourceFiles) {
      if (file.type === 'typescript') {
        try {
          await this.compiler.validateSyntax(file.content);
        } catch (error) {
          errors.push(`文件 ${file.name} 语法错误: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
    }

    // 检查依赖
    for (const dep of project.dependencies) {
      if (!dep.name || !dep.version) {
        errors.push(`依赖信息不完整: ${dep.name || '未知依赖'}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): HTMLExportConfig {
    return { ...DEFAULT_EXPORT_CONFIG };
  }

  /**
   * 预览导出结果
   */
  async preview(project: GameProject): Promise<string> {
    const config = this.getDefaultConfig();
    
    // 简化的预览流程，不进行完整的编译和优化
    const compilationResult = await this.compiler.compile(project, config);
    const bundleResult = await this.bundler.bundle(project, config);
    
    return await this.templateEngine.generate({
      title: project.name,
      description: project.description || '',
      gameCode: compilationResult.code,
      styles: compilationResult.css || '',
      dependencies: bundleResult.dependencies,
      assets: {},
      config: config
    }, config);
  }

  // ==================== 私有方法 ====================

  /**
   * 加载项目
   */
  private async loadProject(projectOrId: string | GameProject): Promise<GameProject> {
    if (typeof projectOrId === 'string') {
      return await this.projectManager.loadProject(projectOrId);
    }
    return projectOrId;
  }

  /**
   * 合并配置
   */
  private mergeConfig(baseConfig: HTMLExportConfig, overrides?: Partial<HTMLExportConfig>): HTMLExportConfig {
    if (!overrides) return baseConfig;
    
    return {
      ...baseConfig,
      ...overrides,
      project: { ...baseConfig.project, ...overrides.project },
      output: { ...baseConfig.output, ...overrides.output },
      compilation: { ...baseConfig.compilation, ...overrides.compilation },
      dependencies: { ...baseConfig.dependencies, ...overrides.dependencies },
      assets: { ...baseConfig.assets, ...overrides.assets },
      optimization: { ...baseConfig.optimization, ...overrides.optimization },
      debug: { ...baseConfig.debug, ...overrides.debug }
    };
  }

  /**
   * 报告进度
   */
  private reportProgress(
    options: ExportOptions, 
    stage: ExportProgress['stage'], 
    progress: number, 
    message: string,
    details?: any
  ): void {
    if (options.onProgress) {
      options.onProgress({
        stage,
        progress,
        message,
        details,
        timestamp: new Date()
      });
    }
  }

  /**
   * 保存输出文件
   */
  private async saveOutput(content: string, config: HTMLExportConfig, outputDir?: string): Promise<string> {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const dir = outputDir || config.output.directory;
    const filename = config.output.filename;
    const outputPath = path.join(dir, filename);
    
    // 确保输出目录存在
    await fs.mkdir(dir, { recursive: true });
    
    // 写入文件
    await fs.writeFile(outputPath, content, 'utf8');
    
    return outputPath;
  }
}

/**
 * 创建HTML导出器实例
 */
export function createHTMLExporter(): HTMLExporter {
  return new HTMLExportCore();
}

/**
 * 快速导出函数
 */
export async function exportToHTML(options: ExportOptions): Promise<ExportResult> {
  const exporter = createHTMLExporter();
  return await exporter.export(options);
}
