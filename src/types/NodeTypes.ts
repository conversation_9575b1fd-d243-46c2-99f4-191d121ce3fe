import * as THREE from 'three';

/**
 * 游戏节点类型枚举
 */
export enum GameNodeType {
  MESH = 'mesh',
  LIGHT = 'light',
  CAMERA = 'camera',
  TRANSFORM = 'transform',
  PARTICLE_SYSTEM = 'particle_system',
  SOUND = 'sound',
  ANIMATION_GROUP = 'animation_group'
}

/**
 * 动画剪辑信息
 */
export interface AnimationClipInfo {
  name: string;
  duration: number;
  tracks: number;
}

/**
 * 动画文件信息
 */
export interface AnimationInfo {
  id: string;
  name: string;
  filePath: string;
  duration: number;
  isDefault: boolean;
  uploadedAt: Date;
  clips: AnimationClipInfo[];
}

/**
 * 骨骼信息
 */
export interface BoneInfo {
  /** 骨骼名称 */
  name: string;
  /** 骨骼在骨架中的索引 */
  index: number;
  /** 父骨骼名称（如果有） */
  parentName?: string;
  /** 骨骼的世界变换矩阵 */
  worldMatrix: number[]; // 4x4矩阵，以数组形式存储
  /** 骨骼的本地变换矩阵 */
  localMatrix: number[]; // 4x4矩阵，以数组形式存储
  /** 子骨骼名称列表 */
  children: string[];
}

/**
 * 骨骼绑定信息
 */
export interface BoneBinding {
  /** 绑定ID */
  id: string;
  /** 目标骨骼名称 */
  boneName: string;
  /** 绑定的节点ID */
  boundNodeId: string;
  /** 绑定的模型ID */
  boundModelId: string;
  /** 相对于骨骼的位置偏移 */
  positionOffset: { x: number; y: number; z: number };
  /** 相对于骨骼的旋转偏移 */
  rotationOffset: { x: number; y: number; z: number };
  /** 相对于骨骼的缩放 */
  scale: { x: number; y: number; z: number };
  /** 是否启用 */
  enabled: boolean;
  /** 创建时间 */
  createdAt: Date;
}

/**
 * 模型文件信息
 */
export interface ModelInfo {
  id: string;
  name: string;
  filePath: string;
  fileSize: number;
  fileType: 'fbx' | 'glb' | 'gltf';
  isDefault: boolean;
  uploadedAt: Date;
  meshCount?: number;
  materialCount?: number;
  hasAnimations?: boolean;
  /** 骨骼信息（如果模型包含骨架） */
  bones?: BoneInfo[];
  /** 骨骼绑定配置 */
  boneBindings?: BoneBinding[];
  /** 是否包含骨架 */
  hasSkeleton?: boolean;
}

/**
 * 材质文件信息
 */
export interface MaterialInfo {
  id: string;
  name: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  isDefault: boolean;
  uploadedAt: Date;
  materialType?: 'diffuse' | 'normal' | 'specular' | 'roughness' | 'metallic' | 'emissive';
}

/**
 * 基础节点属性
 */
export interface BaseNodeProperties {
  id: string;
  name: string;
  type: GameNodeType;
  visible: boolean;
  enabled: boolean;
  position: THREE.Vector3;
  rotation: THREE.Vector3;
  scaling: THREE.Vector3;
  parent?: string; // 父节点ID
  children: string[]; // 子节点ID列表
  metadata?: Record<string, unknown>;
}

/**
 * Mesh节点特有属性
 */
export interface MeshNodeProperties extends BaseNodeProperties {
  type: GameNodeType.MESH;
  geometry: {
    type: 'box' | 'sphere' | 'cylinder' | 'plane' | 'ground' | 'custom';
    parameters?: Record<string, unknown>;
  };
  material?: {
    type: 'standard' | 'pbr' | 'node';
    diffuseColor?: THREE.Color;
    specularColor?: THREE.Color;
    emissiveColor?: THREE.Color;
    ambientColor?: THREE.Color;
    diffuseTexture?: string; // 纹理路径
    normalTexture?: string;
    specularTexture?: string;
    roughness?: number;
    metallic?: number;
    [key: string]: unknown;
  };
  physics?: {
    enabled: boolean;
    mass: number;
    restitution: number;
    friction: number;
    impostor: 'box' | 'sphere' | 'cylinder' | 'mesh';
  };
  collisions?: {
    enabled: boolean;
    checkCollisions: boolean;
    radius?: number;
  };
  animations?: AnimationInfo[];
  models?: ModelInfo[];
  materials?: MaterialInfo[];
}

/**
 * Light节点特有属性
 */
export interface LightNodeProperties extends BaseNodeProperties {
  type: GameNodeType.LIGHT;
  lightType: 'directional' | 'point' | 'spot' | 'hemispheric';
  intensity: number;
  color: THREE.Color;
  range?: number;
  direction?: THREE.Vector3;
  angle?: number; // for spot light
  exponent?: number; // for spot light
  groundColor?: THREE.Color; // for hemispheric light
  shadows?: {
    enabled: boolean;
    mapSize: number;
    bias: number;
  };
}

/**
 * Camera节点特有属性
 */
export interface CameraNodeProperties extends BaseNodeProperties {
  type: GameNodeType.CAMERA;
  cameraType: 'arc_rotate' | 'universal' | 'free' | 'follow';
  target?: THREE.Vector3;
  alpha?: number; // for arc rotate camera
  beta?: number; // for arc rotate camera
  radius?: number; // for arc rotate camera
  fov: number;
  minZ: number;
  maxZ: number;
  speed?: number;
  viewLocked?: boolean; // 视角锁定，默认true
  controls?: {
    enabled: boolean;
    panningSensibility?: number;
    wheelPrecision?: number;
    pinchPrecision?: number;
  };
}

/**
 * 粒子系统节点属性
 */
export interface ParticleSystemNodeProperties extends BaseNodeProperties {
  type: GameNodeType.PARTICLE_SYSTEM;
  emitter: string; // 发射器节点ID
  particleTexture?: string;
  capacity: number;
  emitRate: number;
  minLifeTime: number;
  maxLifeTime: number;
  minSize: number;
  maxSize: number;
  colorDead: THREE.Color;
  color1: THREE.Color;
  color2: THREE.Color;
  gravity: THREE.Vector3;
  direction1: THREE.Vector3;
  direction2: THREE.Vector3;
  minEmitPower: number;
  maxEmitPower: number;
  updateSpeed: number;
  blendMode: number;
}

/**
 * 声音节点属性
 */
export interface SoundNodeProperties extends BaseNodeProperties {
  type: GameNodeType.SOUND;
  soundUrl: string;
  volume: number;
  pitch: number;
  loop: boolean;
  autoplay: boolean;
  is3D: boolean;
  maxDistance?: number;
  rolloffFactor?: number;
  refDistance?: number;
}

/**
 * 动画组节点属性
 */
export interface AnimationGroupNodeProperties extends BaseNodeProperties {
  type: GameNodeType.ANIMATION_GROUP;
  animations: {
    name: string;
    targetNodeId: string;
    property: string;
    dataType: number;
    loopMode: number;
    keys: Array<{
      frame: number;
      value: unknown;
    }>;
  }[];
  from: number;
  to: number;
  loop: boolean;
  speedRatio: number;
}

/**
 * 变换节点属性
 */
export interface TransformNodeProperties extends BaseNodeProperties {
  type: GameNodeType.TRANSFORM;
  // 变换节点主要用于组织层级结构，只包含基础属性
}

/**
 * 联合节点属性类型
 */
export type GameNodeProperties = 
  | MeshNodeProperties 
  | LightNodeProperties 
  | CameraNodeProperties 
  | ParticleSystemNodeProperties
  | SoundNodeProperties
  | AnimationGroupNodeProperties
  | TransformNodeProperties;

/**
 * 节点注册表接口
 */
export interface NodeRegistryInterface {
  register(node: GameNodeProperties): void;
  unregister(nodeId: string): void;
  getNode(nodeId: string): GameNodeProperties | undefined;
  getAllNodes(): GameNodeProperties[];
  getNodesByType(type: GameNodeType): GameNodeProperties[];
  updateNode(nodeId: string, updates: Partial<GameNodeProperties>): void;
  clear(): void;
}

/**
 * 节点变更事件
 */
export interface NodeChangeEvent {
  type: 'add' | 'remove' | 'update';
  nodeId: string;
  node?: GameNodeProperties;
  changes?: Partial<GameNodeProperties>;
}

/**
 * 节点变更监听器
 */
export type NodeChangeListener = (event: NodeChangeEvent) => void; 