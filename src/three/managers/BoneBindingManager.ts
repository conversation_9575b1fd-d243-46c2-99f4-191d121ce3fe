/**
 * BoneBindingManager - 骨骼绑定管理器
 * 负责在Three.js场景中应用和管理骨骼绑定
 */

import * as THREE from 'three';
import { BoneBinding, ModelInfo } from '../../types/NodeTypes';

export interface BoneBindingContext {
  /** 绑定的源模型对象 */
  sourceModel: THREE.Group;
  /** 绑定的目标模型对象 */
  targetModel: THREE.Group;
  /** 绑定配置 */
  binding: BoneBinding;
  /** 目标骨骼对象 */
  targetBone?: THREE.Bone;
}

export class BoneBindingManager {
  private scene: THREE.Scene;
  private activeBindings: Map<string, BoneBindingContext[]> = new Map();

  constructor(scene: THREE.Scene) {
    this.scene = scene;
  }

  /**
   * 应用节点的所有骨骼绑定
   * @param nodeId 节点ID
   * @param models 节点的模型列表
   */
  public applyNodeBoneBindings(nodeId: string, models: ModelInfo[]): void {
    console.log(`[骨骼绑定] 开始应用节点 ${nodeId} 的骨骼绑定`);

    // 清除现有绑定
    this.clearNodeBindings(nodeId);

    // 查找包含骨骼绑定的模型
    const modelsWithBindings = models.filter(model => 
      model.boneBindings && model.boneBindings.length > 0
    );

    if (modelsWithBindings.length === 0) {
      console.log(`[骨骼绑定] 节点 ${nodeId} 没有骨骼绑定`);
      return;
    }

    const nodeBindings: BoneBindingContext[] = [];

    modelsWithBindings.forEach(sourceModel => {
      const sourceObject = this.scene.getObjectByName(nodeId);
      if (!sourceObject) {
        console.warn(`[骨骼绑定] 未找到源节点对象: ${nodeId}`);
        return;
      }

      sourceModel.boneBindings!.forEach(binding => {
        if (!binding.enabled) {
          console.log(`[骨骼绑定] 跳过已禁用的绑定: ${binding.id}`);
          return;
        }

        try {
          const bindingContext = this.createBoneBinding(
            sourceObject as THREE.Group,
            binding,
            models
          );

          if (bindingContext) {
            nodeBindings.push(bindingContext);
            console.log(`[骨骼绑定] 成功创建绑定: ${binding.id}`);
          }
        } catch (error) {
          console.error(`[骨骼绑定] 创建绑定失败: ${binding.id}`, error);
        }
      });
    });

    if (nodeBindings.length > 0) {
      this.activeBindings.set(nodeId, nodeBindings);
      console.log(`[骨骼绑定] 节点 ${nodeId} 应用了 ${nodeBindings.length} 个骨骼绑定`);
    }
  }

  /**
   * 创建单个骨骼绑定
   * @param sourceModel 源模型对象
   * @param binding 绑定配置
   * @param allModels 所有可用模型
   * @returns 绑定上下文
   */
  private createBoneBinding(
    sourceModel: THREE.Group,
    binding: BoneBinding,
    allModels: ModelInfo[]
  ): BoneBindingContext | null {
    // 查找目标骨骼
    const targetBone = this.findBoneInModel(sourceModel, binding.boneName);
    if (!targetBone) {
      console.warn(`[骨骼绑定] 未找到目标骨骼: ${binding.boneName}`);
      return null;
    }

    // 查找绑定的目标节点
    const boundNodeObject = this.scene.getObjectByName(binding.boundNodeId);
    if (!boundNodeObject) {
      console.warn(`[骨骼绑定] 未找到绑定目标节点: ${binding.boundNodeId}`);
      return null;
    }

    // 查找绑定的目标模型
    const boundModel = allModels.find(model => model.id === binding.boundModelId);
    if (!boundModel) {
      console.warn(`[骨骼绑定] 未找到绑定目标模型: ${binding.boundModelId}`);
      return null;
    }

    // 克隆目标模型对象
    const targetModel = boundNodeObject.clone() as THREE.Group;
    targetModel.name = `${binding.boundNodeId}_bound_to_${binding.boneName}`;

    // 应用绑定变换
    this.applyBindingTransform(targetModel, binding);

    // 将目标模型附加到骨骼
    targetBone.add(targetModel);

    const bindingContext: BoneBindingContext = {
      sourceModel,
      targetModel,
      binding,
      targetBone
    };

    return bindingContext;
  }

  /**
   * 在模型中查找指定名称的骨骼
   * @param model 模型对象
   * @param boneName 骨骼名称
   * @returns 骨骼对象或null
   */
  private findBoneInModel(model: THREE.Group, boneName: string): THREE.Bone | null {
    let foundBone: THREE.Bone | null = null;

    model.traverse((child) => {
      if (child instanceof THREE.Bone && child.name === boneName) {
        foundBone = child;
      }
    });

    return foundBone;
  }

  /**
   * 应用绑定变换到目标模型
   * @param targetModel 目标模型
   * @param binding 绑定配置
   */
  private applyBindingTransform(targetModel: THREE.Group, binding: BoneBinding): void {
    // 应用位置偏移
    targetModel.position.set(
      binding.positionOffset.x,
      binding.positionOffset.y,
      binding.positionOffset.z
    );

    // 应用旋转偏移
    targetModel.rotation.set(
      binding.rotationOffset.x,
      binding.rotationOffset.y,
      binding.rotationOffset.z
    );

    // 应用缩放
    targetModel.scale.set(
      binding.scale.x,
      binding.scale.y,
      binding.scale.z
    );

    console.log(`[骨骼绑定] 应用变换:`, {
      position: targetModel.position.toArray(),
      rotation: targetModel.rotation.toArray(),
      scale: targetModel.scale.toArray()
    });
  }

  /**
   * 清除节点的所有骨骼绑定
   * @param nodeId 节点ID
   */
  public clearNodeBindings(nodeId: string): void {
    const bindings = this.activeBindings.get(nodeId);
    if (!bindings) {
      return;
    }

    bindings.forEach(bindingContext => {
      if (bindingContext.targetBone && bindingContext.targetModel) {
        bindingContext.targetBone.remove(bindingContext.targetModel);
        console.log(`[骨骼绑定] 已移除绑定: ${bindingContext.binding.id}`);
      }
    });

    this.activeBindings.delete(nodeId);
    console.log(`[骨骼绑定] 已清除节点 ${nodeId} 的所有绑定`);
  }

  /**
   * 更新骨骼绑定（重新应用）
   * @param nodeId 节点ID
   * @param models 更新后的模型列表
   */
  public updateNodeBoneBindings(nodeId: string, models: ModelInfo[]): void {
    console.log(`[骨骼绑定] 更新节点 ${nodeId} 的骨骼绑定`);
    this.applyNodeBoneBindings(nodeId, models);
  }

  /**
   * 获取节点的活跃绑定数量
   * @param nodeId 节点ID
   * @returns 绑定数量
   */
  public getNodeBindingCount(nodeId: string): number {
    const bindings = this.activeBindings.get(nodeId);
    return bindings ? bindings.length : 0;
  }

  /**
   * 获取所有活跃绑定的统计信息
   * @returns 绑定统计
   */
  public getBindingStats(): { totalNodes: number; totalBindings: number } {
    const totalNodes = this.activeBindings.size;
    let totalBindings = 0;

    this.activeBindings.forEach(bindings => {
      totalBindings += bindings.length;
    });

    return { totalNodes, totalBindings };
  }

  /**
   * 清除所有骨骼绑定
   */
  public clearAllBindings(): void {
    this.activeBindings.forEach((bindings, nodeId) => {
      this.clearNodeBindings(nodeId);
    });
    console.log('[骨骼绑定] 已清除所有骨骼绑定');
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.clearAllBindings();
    this.activeBindings.clear();
    console.log('[骨骼绑定] 骨骼绑定管理器已销毁');
  }
}

export default BoneBindingManager;
