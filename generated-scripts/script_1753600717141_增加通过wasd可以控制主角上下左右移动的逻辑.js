/**
 * 自动生成的脚本文件
 * 
 * 脚本ID: script_1753600717141
 * 脚本名称: 增加通过wasd可以控制主角上下左右移动的逻辑
 * 生成时间: 2025/7/27 15:18:37
 * 功能类型: utility
 * 目标节点: mesh
 * 依赖项: three
 * 描述: 增加通过wasd可以控制主角上下左右移动的逻辑
 */

function executeScript(scene, camera, renderer) {
  // 获取主角对象
  const player = scene.getObjectByName('box_1753425549287');
  if (!player) {
    console.warn('Player object not found');
    return;
  }

  // 键盘状态
  const keys = {
    w: false,
    a: false,
    s: false,
    d: false
  };

  // 移动速度
  const moveSpeed = 0.1;

  // 键盘事件监听
  function onKeyDown(event) {
    const key = event.key.toLowerCase();
    if (keys.hasOwnProperty(key)) {
      keys[key] = true;
    }
  }

  function onKeyUp(event) {
    const key = event.key.toLowerCase();
    if (keys.hasOwnProperty(key)) {
      keys[key] = false;
    }
  }

  // 更新主角位置
  function updatePlayerMovement() {
    if (keys.w) player.position.z -= moveSpeed; // 前进
    if (keys.s) player.position.z += moveSpeed; // 后退
    if (keys.a) player.position.x -= moveSpeed; // 左移
    if (keys.d) player.position.x += moveSpeed; // 右移
  }

  // 动画循环
  function animate() {
    updatePlayerMovement();
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
  }

  // 添加事件监听
  document.addEventListener('keydown', onKeyDown);
  document.addEventListener('keyup', onKeyUp);

  // 开始动画循环
  animate();

  return function cleanup() {
    document.removeEventListener('keydown', onKeyDown);
    document.removeEventListener('keyup', onKeyUp);
  };
}