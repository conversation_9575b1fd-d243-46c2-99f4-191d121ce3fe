"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink,
  Loader2
} from 'lucide-react';

interface ExportResult {
  success: boolean;
  message?: string;
  downloadUrl?: string;
  fileSize?: number;
  filename?: string;
  error?: string;
}

export default function ExportTestPage() {
  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState<ExportResult | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  
  // 表单状态
  const [filename, setFilename] = useState('airplane-game.html');
  const [title, setTitle] = useState('打飞机游戏');
  const [minify, setMinify] = useState(false);

  /**
   * 执行导出
   */
  const handleExport = async () => {
    setIsExporting(true);
    setExportResult(null);
    setExportProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      console.log('🚀 开始导出打飞机游戏...');

      const response = await fetch('/api/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gameType: 'airplane',
          filename,
          title,
          minify
        })
      });

      const result = await response.json();

      clearInterval(progressInterval);
      setExportProgress(100);

      setExportResult(result);

      if (result.success) {
        console.log('✅ 导出成功:', result);
      } else {
        console.error('❌ 导出失败:', result.error);
      }

    } catch (error) {
      console.error('❌ 导出请求失败:', error);
      setExportResult({
        success: false,
        error: error instanceof Error ? error.message : '网络请求失败'
      });
      setExportProgress(0);
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * 下载文件
   */
  const handleDownload = () => {
    if (exportResult?.downloadUrl) {
      const link = document.createElement('a');
      link.href = exportResult.downloadUrl;
      link.download = exportResult.filename || 'airplane-game.html';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  /**
   * 在新窗口中预览
   */
  const handlePreview = () => {
    if (exportResult?.downloadUrl) {
      window.open(exportResult.downloadUrl, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🎮 HTML导出测试
          </h1>
          <p className="text-gray-600">
            测试打飞机游戏的HTML导出功能
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 导出配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                导出配置
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="filename">文件名</Label>
                <Input
                  id="filename"
                  value={filename}
                  onChange={(e) => setFilename(e.target.value)}
                  placeholder="airplane-game.html"
                />
              </div>

              <div>
                <Label htmlFor="title">游戏标题</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="打飞机游戏"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="minify">压缩代码</Label>
                <Switch
                  id="minify"
                  checked={minify}
                  onCheckedChange={setMinify}
                />
              </div>

              <Button
                onClick={handleExport}
                disabled={isExporting}
                className="w-full"
                size="lg"
              >
                {isExporting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    导出中...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    导出HTML文件
                  </>
                )}
              </Button>

              {/* 进度条 */}
              {isExporting && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>导出进度</span>
                    <span>{exportProgress}%</span>
                  </div>
                  <Progress value={exportProgress} className="w-full" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 导出结果 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {exportResult?.success ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : exportResult?.error ? (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <FileText className="w-5 h-5" />
                )}
                导出结果
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!exportResult ? (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>点击导出按钮开始导出</p>
                </div>
              ) : exportResult.success ? (
                <div className="space-y-4">
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      {exportResult.message || '导出成功！'}
                    </AlertDescription>
                  </Alert>

                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">文件名:</span>
                      <span className="text-sm font-medium">{exportResult.filename}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">文件大小:</span>
                      <span className="text-sm font-medium">
                        {exportResult.fileSize ? `${(exportResult.fileSize / 1024).toFixed(2)} KB` : '未知'}
                      </span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button onClick={handleDownload} className="flex-1">
                      <Download className="w-4 h-4 mr-2" />
                      下载文件
                    </Button>
                    <Button onClick={handlePreview} variant="outline" className="flex-1">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      预览游戏
                    </Button>
                  </div>
                </div>
              ) : (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    导出失败: {exportResult.error}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 功能说明 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>功能说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">导出功能</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• 将打飞机游戏导出为独立HTML文件</li>
                  <li>• 包含完整的游戏逻辑和UI</li>
                  <li>• 使用Babylon.js CDN加载3D引擎</li>
                  <li>• 支持移动端和桌面端</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">游戏特性</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• 3D飞机射击游戏</li>
                  <li>• 点击或空格键射击</li>
                  <li>• 实时血量显示</li>
                  <li>• 胜利条件检测</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
