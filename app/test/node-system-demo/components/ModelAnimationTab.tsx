/**
 * ModelAnimationTab - 模型动画管理标签页组件
 * 提供模型和动画文件的上传和管理功能
 */

'use client';

import React, { useState, useCallback } from 'react';
import { GameNodeType, GameNodeProperties, MeshNodeProperties, AnimationInfo, ModelInfo, BoneBinding } from '../../../../src/types/NodeTypes';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import AnimationManager from './AnimationManager';
import ModelManager from './ModelManager';
import BoneBindingPanel from './BoneBindingPanel';

interface ModelAnimationTabProps {
  selectedNode: GameNodeProperties;
  isDragOver: boolean;
  isDragOverAnimation: boolean;
  isUploadingModel: boolean;
  isUploadingAnimation: boolean;
  uploadError: string | null;
  animationError: string | null;
  onSetIsDragOver: (dragOver: boolean) => void;
  onSetIsDragOverAnimation: (dragOver: boolean) => void;
  onModelUpload: (file: File, nodeId: string) => void;
  onAnimationUpload: (file: File, nodeId: string) => void;
  onPreviewAnimation: (nodeId: string, animation: AnimationInfo) => void;
  onStopPreview: (nodeId: string) => void;
  onSetDefaultAnimation: (nodeId: string, animation: AnimationInfo) => void;
  onRemoveAnimation: (nodeId: string, animationId: string) => void;
  onPreviewModel: (nodeId: string, model: ModelInfo) => void;
  onSetDefaultModel: (nodeId: string, model: ModelInfo) => void;
  onRemoveModel: (nodeId: string, modelId: string) => void;
}

export const ModelAnimationTab: React.FC<ModelAnimationTabProps> = ({
  selectedNode,
  isDragOver,
  isDragOverAnimation,
  isUploadingModel,
  isUploadingAnimation,
  uploadError,
  animationError,
  onSetIsDragOver,
  onSetIsDragOverAnimation,
  onModelUpload,
  onAnimationUpload,
  onPreviewAnimation,
  onStopPreview,
  onSetDefaultAnimation,
  onRemoveAnimation,
  onPreviewModel,
  onSetDefaultModel,
  onRemoveModel
}) => {
  const { getNodeAnimations, getNodeModels, currentModel } = useNodeSystem();
  const [selectedModelForBinding, setSelectedModelForBinding] = useState<ModelInfo | null>(null);
  const [showBoneBinding, setShowBoneBinding] = useState(false);

  // 处理骨骼绑定变更
  const handleBoneBindingChange = useCallback(async (nodeId: string, modelId: string, bindings: BoneBinding[]) => {
    try {
      console.log(`[骨骼绑定] 开始更新节点 ${nodeId} 模型 ${modelId} 的骨骼绑定`);

      // 获取当前节点的模型列表
      const currentModels = getNodeModels(nodeId) || [];

      // 更新指定模型的骨骼绑定
      const updatedModels = currentModels.map(model =>
        model.id === modelId
          ? { ...model, boneBindings: bindings }
          : model
      );

      // 直接调用API保存更新后的模型数据
      const response = await fetch('/api/node-properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodeProperty: {
            id: nodeId,
            models: updatedModels
          }
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log(`[骨骼绑定] 已更新节点 ${nodeId} 模型 ${modelId} 的骨骼绑定:`, bindings);

        // 触发骨骼绑定管理器更新
        const boneBindingManager = (window as unknown as {
          boneBindingManager?: {
            updateNodeBoneBindings: (nodeId: string, models: ModelInfo[]) => void
          }
        }).boneBindingManager;

        if (boneBindingManager) {
          boneBindingManager.updateNodeBoneBindings(nodeId, updatedModels);
        }
      } else {
        throw new Error(result.error || '保存失败');
      }
    } catch (error) {
      console.error('[骨骼绑定] 更新失败:', error);
    }
  }, [getNodeModels]);

  // 显示骨骼绑定面板
  const handleShowBoneBinding = useCallback((model: ModelInfo) => {
    setSelectedModelForBinding(model);
    setShowBoneBinding(true);
  }, []);

  // 隐藏骨骼绑定面板
  const handleHideBoneBinding = useCallback(() => {
    setSelectedModelForBinding(null);
    setShowBoneBinding(false);
  }, []);

  if (selectedNode.type !== GameNodeType.MESH) {
    return (
      <div className="text-center py-6 text-gray-500">
        <p className="text-sm">此节点类型不支持模型和动画</p>
      </div>
    );
  }

  const meshNode = selectedNode as MeshNodeProperties;
  // 从Context获取动画数据，而不是从节点属性
  const nodeAnimations = getNodeAnimations(selectedNode.id);

  return (
    <div className="space-y-6">
      {/* 模型上传区域 */}
      <div>
        <h4 className="text-sm font-medium mb-3 text-gray-900 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
          模型文件
        </h4>
        <div
          className={`border-2 border-dashed rounded-lg py-1 px-2 text-center transition-colors ${
            isDragOver
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-200'
          }`}
          onDragOver={(e) => {
            e.preventDefault();
            onSetIsDragOver(true);
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            onSetIsDragOver(false);
          }}
          onDrop={(e) => {
            e.preventDefault();
            onSetIsDragOver(false);
            const files = Array.from(e.dataTransfer.files);
            const file = files[0];
            if (file) {
              onModelUpload(file, selectedNode.id);
            }
          }}
        >
          <input
            type="file"
            accept=".fbx,.glb,.gltf"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                onModelUpload(file, selectedNode.id);
              }
            }}
            className="hidden"
            id="model-upload"
            disabled={isUploadingModel}
          />
          <label
            htmlFor="model-upload"
            className={`flex flex-col items-center justify-center cursor-pointer ${
              isUploadingModel ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <div className="w-8 h-8 mb-2 text-blue-600">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            {isUploadingModel ? (
              <div className="flex flex-col items-center">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                <p className="text-sm text-gray-600">正在处理模型...</p>
              </div>
            ) : (
              <>
                <p className="text-sm text-gray-700 mb-1">
                  {isDragOver ? '松开以上传模型' : '点击或拖拽上传模型文件'}
                </p>
                <p className="text-xs text-gray-600">支持 .fbx、.glb 和 .gltf 格式</p>
              </>
            )}
          </label>
        </div>
        
        {uploadError && (
          <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-800">
            {uploadError}
          </div>
        )}
      </div>

      {/* 模型管理器 */}
      <div>
        <h4 className="text-sm font-medium mb-3 text-gray-900 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          模型管理
        </h4>

        {/* 模型管理器 */}
        <ModelManager
          nodeId={selectedNode.id}
          models={getNodeModels(selectedNode.id)}
          currentModelId={currentModel.get(selectedNode.id)}
          onPreviewModel={onPreviewModel}
          onSetDefaultModel={onSetDefaultModel}
          onRemoveModel={onRemoveModel}
          onShowBoneBinding={handleShowBoneBinding}
          isUploadingModel={isUploadingModel}
          uploadError={uploadError}
        />
      </div>

      {/* 分隔线 */}
      <hr className="border-gray-200" />

      {/* 动画管理区域 */}
      <div>
        <h4 className="text-sm font-medium mb-3 text-gray-900 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
          </svg>
          动画文件
        </h4>

        {/* 动画上传区域 */}
        <div className="mb-4">
          <div
            className={`border-2 border-dashed rounded-lg py-1 px-2 text-center transition-colors ${
              isDragOverAnimation
                ? 'border-green-500 bg-green-50'
                : 'border-gray-300 hover:border-gray-200'
            }`}
            onDragOver={(e) => {
              e.preventDefault();
              onSetIsDragOverAnimation(true);
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              onSetIsDragOverAnimation(false);
            }}
            onDrop={(e) => {
              e.preventDefault();
              onSetIsDragOverAnimation(false);
              const files = Array.from(e.dataTransfer.files);
              const file = files[0];
              if (file) {
                onAnimationUpload(file, selectedNode.id);
              }
            }}
          >
            <input
              type="file"
              accept=".fbx"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  onAnimationUpload(file, selectedNode.id);
                }
              }}
              className="hidden"
              id="animation-upload"
              disabled={isUploadingAnimation}
            />
            <label
              htmlFor="animation-upload"
              className={`flex flex-col items-center justify-center cursor-pointer ${
                isUploadingAnimation ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <div className="w-8 h-8 mb-2 text-green-600">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 012-2z" />
                </svg>
              </div>
              {isUploadingAnimation ? (
                <div className="flex flex-col items-center">
                  <div className="w-6 h-6 border-2 border-green-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                  <p className="text-sm text-gray-600">正在处理动画...</p>
                </div>
              ) : (
                <>
                  <p className="text-sm text-gray-700 mb-1">
                    {isDragOverAnimation ? '松开以上传动画' : '点击或拖拽上传动画文件'}
                  </p>
                  <p className="text-xs text-gray-600">支持 .fbx 格式</p>
                </>
              )}
            </label>
          </div>
        </div>

        {/* 动画管理器 */}
        <AnimationManager
          nodeId={selectedNode.id}
          animations={nodeAnimations}
          currentPlayingAnimationId={undefined}
          previewingAnimationId={undefined}
          onPreviewAnimation={onPreviewAnimation}
          onStopPreview={onStopPreview}
          onSetDefaultAnimation={onSetDefaultAnimation}
          onRemoveAnimation={onRemoveAnimation}
          isUploadingAnimation={isUploadingAnimation}
          animationError={animationError}
        />
      </div>

      {/* 骨骼绑定面板 */}
      {showBoneBinding && selectedModelForBinding && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-900 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              骨骼绑定 - {selectedModelForBinding.name}
            </h4>
            <button
              onClick={handleHideBoneBinding}
              className="p-1 text-gray-500 hover:text-gray-700 rounded"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <BoneBindingPanel
            selectedNode={selectedNode}
            selectedModel={selectedModelForBinding}
            onBoneBindingChange={handleBoneBindingChange}
          />
        </div>
      )}
    </div>
  );
};

export default ModelAnimationTab;
